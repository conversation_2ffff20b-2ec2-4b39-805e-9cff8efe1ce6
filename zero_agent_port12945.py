       # Extract context and token limits
    context = data.get("context", {})
        max_tokens = context.get("maxTokens", 1000)
        response_limit = context.get("responseLimit", 1000) 
        token_limit = context.get("tokenLimit", 1000)
        
        # Route commands
        if command == "get_status":
            response = await self.get_status()
        elif command == "chat":
            response = await self.handle_chat(
                data.get("message", ""), 
                max_tokens=max_tokens,
                context=context
            )
        elif command == "analyze_code":
            response = await self.analyze_code(data.get("code", ""))
        elif command == "harmonic_sync":
            response = await self.harmonic_sync()
        # Trinity Integration Commands
        elif command == "voice_command":
            response = self.process_voice_command(data.get("voice_input", ""))
        elif command == "security_dashboard":
            response = self.generate_security_dashboard_data()
        elif command == "harmonic_analysis":
            response = self.apply_harmonic_analysis(data.get("signal_data", []))
        elif command == "trinity_status":
            response = {
                "trinity_agents": self.generate_security_dashboard_data()["trinity_agents"],
                "formation": self.generate_security_dashboard_data()["formation"],
                "timestamp": datetime.now().isoformat()
            }
        else:
            response = {"error": f"Unknown command: {command}"}